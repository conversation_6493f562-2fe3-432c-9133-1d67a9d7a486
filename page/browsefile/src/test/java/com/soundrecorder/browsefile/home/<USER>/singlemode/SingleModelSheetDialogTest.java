package com.soundrecorder.browsefile.home.dialog.singlemode;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;
import org.robolectric.Robolectric;
import org.robolectric.RuntimeEnvironment;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowDialog;

import androidx.recyclerview.widget.COUIRecyclerView;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import static org.mockito.Mockito.spy;
import static org.robolectric.Shadows.shadowOf;

import androidx.appcompat.app.AlertDialog;
import android.os.Build;

import com.coui.appcompat.panel.COUIBottomSheetDialog;

import com.soundrecorder.browsefile.BrowseFile;
import com.soundrecorder.browsefile.shadows.ShadowCursorHelper;
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption;
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class SingleModelSheetDialogTest {
    private final String FILED_DIALOG_NAME = "mDialog";
    private BrowseFile mActivity;
    private BrowseFile mSpyActivity;

    @Before
    public void setUp() {
        mActivity = Robolectric.buildActivity(BrowseFile.class).get();
        mSpyActivity = spy(mActivity);
    }

    @Test
    public void should_notNull_when_init() {
        SingleModelSheetDialog sheetDialog = new SingleModelSheetDialog(mSpyActivity, true);
        sheetDialog.init();
        AlertDialog recyclerView = Whitebox.getInternalState(sheetDialog, FILED_DIALOG_NAME);
        Assert.assertNotNull(recyclerView);
    }

    @Test
    public void should_notNull_when_create() {
        SingleModelSheetDialog sheetDialog = new SingleModelSheetDialog(mSpyActivity, true);
        Assert.assertNotNull(sheetDialog.create());
    }

    @Ignore
    @Test
    @Config(sdk = Build.VERSION_CODES.S, shadows = ShadowCursorHelper.class)
    public void should_dialog_when_showOrdismiss() {
        SingleModelSheetDialog sheetDialog = new SingleModelSheetDialog(mSpyActivity, true);
        sheetDialog.show(0);
        ShadowDialog dialog = shadowOf(RuntimeEnvironment.application).getLatestDialog();
        Assert.assertNotNull(dialog);
        sheetDialog.dismiss();
        Assert.assertNotNull(dialog);
        sheetDialog.release();
        COUIBottomSheetDialog mDialog = Whitebox.getInternalState(sheetDialog, "mDialog");
        Assert.assertNull(mDialog);
    }

    @After
    public void tearDown() {
        mActivity = null;
        mSpyActivity = null;
    }

}
