/*********************************************************************
 * * Copyright (C), 2021, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/8/31
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.search.load.center.localsync

import com.soundrecorder.browsefile.search.load.center.CenterDbUtils
import com.soundrecorder.browsefile.search.load.center.databean.SearchInsertBean
import com.soundrecorder.browsefile.home.item.ItemBrowseRecordViewModel
import com.soundrecorder.base.utils.DebugUtil
import java.util.*

object CenterLocalStorageManager {

    private const val TAG = "CenterLocalStorageManager"

    /**
     * read the local storage, and convert the str to standard bean
     */
    @JvmStatic
    private fun getSavedSyncInfo(): CenterLocalStorageBean? {
        val curStorage = CenterDbUtils.getSyncedMediaInfo()
        return if (curStorage.isNullOrEmpty()) {
            null
        } else {
            CenterLocalStorageBean.convertStrToBean(curStorage)
        }
    }

    /**
     * delete the saved media info from the local storage
     */
    @JvmStatic
    fun deleteListToLocal(deleteList: List<Long>) {
        if (deleteList.isNullOrEmpty()) {
            return
        }

        val originList = getSavedSyncInfo()?.originList
        DebugUtil.d(TAG, "deleteListToLocal, originList is $originList")
        if (originList.isNullOrEmpty().not()) {
            deleteList(deleteList, originList!!)
            val modifyStr = CenterLocalStorageBean(originList).toString()
            CenterDbUtils.saveSyncedMediaInfo(modifyStr)
            DebugUtil.d(TAG, "deleteListToLocal, modifyStr is $modifyStr")
        }
    }

    /**
     * update the saved media info from the local storage
     */
    @JvmStatic
    fun <T> updateListToLocal(updateList: List<T>) {
        calUpdateListToLocal(updateList)?.let {
            val modifyStr = it.toString()

            DebugUtil.d(TAG, "updateListToLocal, modifyStr is $modifyStr")
            CenterDbUtils.saveSyncedMediaInfo(modifyStr)
        }
    }

    @JvmStatic
    fun <T> calUpdateListToLocal(updateList: List<T>): CenterLocalStorageBean? {
        if (updateList.isNullOrEmpty()) {
            return null
        }

        val originList = getSavedSyncInfo()?.originList
        DebugUtil.d(TAG, "updateListToLocal, originList is $originList")
        return if (originList.isNullOrEmpty()) {
            val newList = createStorageItemList(updateList)
            CenterLocalStorageBean(newList)
        } else {
            updateList(updateList, originList)
            CenterLocalStorageBean(originList)
        }
    }

    /**
     * create storageItemList from given list
     * @param needSort whether sort the new list. if you can confirm the input list is in order, then you can pass false to avoid extra sort
     */
    @JvmStatic
    fun <T> createStorageItemList(
        list: List<T>,
        needSort: Boolean = true
    ): MutableList<CenterLocalStorageItem> {
        return mutableListOf<CenterLocalStorageItem>().apply {
            for (item in list) {
                createStorageItem(item)?.let { newItem ->
                    add(newItem)
                }
            }
            if (needSort) {
                sortByDescending { it.mediaId }
            }
        }
    }

    @JvmStatic
    private fun <T> createStorageItem(item: T): CenterLocalStorageItem? {
        return when (item) {
            is ItemBrowseRecordViewModel -> CenterLocalStorageItem(item.mediaId, item.dateModified)
            is SearchInsertBean -> CenterLocalStorageItem(item.id, item.date_modified)
            is CenterLocalStorageItem -> item
            else -> null
        }
    }

    @JvmStatic
    fun <T> updateList(
        updateList: List<T>,
        originList: MutableList<CenterLocalStorageItem>
    ) {
        for (item in updateList) {
            createStorageItem(item)?.let { newItem ->
                val index = Collections.binarySearch(originList, newItem
                ) { o1, o2 -> o2.mediaId.compareTo(o1.mediaId) }
                DebugUtil.d(TAG, "updateList, find item pos is $index")
                if (index >= 0) {
                    //found the original item
                    originList[index] = newItem
                } else {
                    val realIndex = -1 * (index + 1)
                    if (realIndex >= originList.size) {
                        originList.add(newItem)
                    } else {
                        originList.add(realIndex, newItem)
                    }
                }
            }
        }
    }

    @JvmStatic
    fun deleteList(
        deleteList: List<Long>,
        originList: MutableList<CenterLocalStorageItem>
    ) {
        for (item in deleteList) {
            val index = Collections.binarySearch(originList, CenterLocalStorageItem(item, 0)
            ) { o1, o2 -> o2.mediaId.compareTo(o1.mediaId) }
            DebugUtil.d(TAG, "deleteList, find item pos is $index")
            if (index >= 0) {
                originList.removeAt(index)
            }
        }
    }
}