package com.soundrecorder.setting.setting;

import static android.app.Activity.RESULT_OK;
import static com.soundrecorder.base.utils.ClickUtils.DURATION_500;
import static com.soundrecorder.common.permission.PermissionUtils.hasFuncTypePermission;
import static com.soundrecorder.common.utils.FunctionOption.hasSupportByte;
import static com.soundrecorder.modulerouter.privacyPolicy.IPrivacyPolicyDelegate.POLICY_TYPE_COMMON;
import static com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_PERMISSION_SMART_SHORTHAND;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;

import androidx.activity.result.ActivityResultLauncher;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.preference.Preference;

import com.coui.appcompat.contextutil.COUIContextUtil;
import com.coui.appcompat.preference.COUIJumpPreference;
import com.coui.appcompat.preference.COUIPreference;
import com.coui.appcompat.preference.COUIPreferenceCategory;
import com.coui.appcompat.preference.COUIRecommendedPreference;
import com.coui.appcompat.preference.COUISwitchPreference;
import com.coui.appcompat.toolbar.COUIToolbar;
import com.soundrecorder.base.BaseActivity;
import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.AppUtil;
import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.base.utils.ClickUtils;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.LanguageUtil;
import com.soundrecorder.base.utils.NetworkUtils;
import com.soundrecorder.base.utils.OpenIdUtils;
import com.soundrecorder.base.utils.PrefUtil;
import com.soundrecorder.base.utils.ToastManager;
import com.soundrecorder.base.view.RootViewPersistentInsetsCallback;
import com.soundrecorder.common.buryingpoint.BuryingPoint;
import com.soundrecorder.common.buryingpoint.RecorderUserAction;
import com.soundrecorder.common.constant.RecorderConstant;
import com.soundrecorder.common.flexible.FollowHandPanelUtils;
import com.soundrecorder.common.permission.PermissionDialogUtils;
import com.soundrecorder.common.permission.PermissionProxyActivity;
import com.soundrecorder.common.permission.PermissionUtils;
import com.soundrecorder.common.task.AppTaskUtil;
import com.soundrecorder.common.utils.FunctionOption;
import com.soundrecorder.common.utils.OPSettingUtils;
import com.soundrecorder.common.utils.RecordModeUtil;
import com.soundrecorder.common.utils.TipUtil;
import com.soundrecorder.common.utils.ViewUtils;
import com.soundrecorder.common.utils.taskbar.TaskBarUtil;
import com.soundrecorder.common.widget.AutoIndentPreferenceFragment;
import com.soundrecorder.modulerouter.FeedBackInterface;
import com.soundrecorder.modulerouter.SettingInterface;
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface;
import com.soundrecorder.modulerouter.cloudkit.CloudSwitchState;
import com.soundrecorder.modulerouter.cloudkit.CloudTipManagerAction;
import com.soundrecorder.modulerouter.convertService.ConvertSupportAction;
import com.soundrecorder.modulerouter.privacyPolicy.IPrivacyPolicyDelegate;
import com.soundrecorder.modulerouter.privacyPolicy.IPrivacyPolicyResultListener;
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyInterface;
import com.soundrecorder.modulerouter.recorder.RecordInterface;
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface;
import com.soundrecorder.modulerouter.smartname.IPluginDownloadCallback;
import com.soundrecorder.modulerouter.smartname.IUnifiedSummaryCallBack;
import com.soundrecorder.modulerouter.smartname.SmartNameAction;
import com.soundrecorder.modulerouter.translate.AIAsrManagerAction;
import com.soundrecorder.modulerouter.translate.IAsrPluginCallBack;
import com.soundrecorder.modulerouter.utils.KoinInterfaceHelper;
import com.soundrecorder.setting.R;
import com.soundrecorder.setting.about.RecordAboutActivity;
import com.soundrecorder.setting.privacypolicy.PrivacyPolicyActivity;
import com.soundrecorder.setting.setting.callsummary.ThirdAppRecordCheckManager;
import com.soundrecorder.setting.setting.callsummary.ThirdRecordResultBean;
import com.soundrecorder.setting.setting.dialog.RecordModeDialog;
import com.soundrecorder.setting.setting.dialog.SelectLanguageDialog;
import com.soundrecorder.setting.setting.dialog.SelectRecordAudioFormatDialog;
import com.soundrecorder.setting.setting.localdata.LocalCountryData;
import com.soundrecorder.setting.widget.SmartNamePreference;

import org.jetbrains.annotations.NotNull;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import kotlin.Unit;

public class SettingFragment extends AutoIndentPreferenceFragment implements Preference.OnPreferenceChangeListener,
        COUIJumpPreference.OnPreferenceClickListener,
        IPrivacyPolicyResultListener {

    private static final String TAG = "SetRecorder";
    // 录制音频格式选择
    public static final String JUMP_OP_EXP_AUDIO_FORMAT = "pref_setting_record_format";
    public static final String JUMP_OP_EXP_AUDIO_MODE = "pref_setting_record_mode";
    public static final String JUMP_THIRD_APP_RECORD_SETTING = "pref_setting_record_for_third_app";
    public static final String SETTING_RECORD_SYNC = "pref_setting_record_sync";
    public static final String SETTING_RECORD_SMART_NAME = "pref_setting_record_smart_name";
    public static final String CATEGORY_TRANSCRIBE = "pref_category_transcribe";
    public static final String SETTING_SPEECH_LANGUAGE = "pref_setting_speech_language";

    // Conversion settings
    public static final String SETTING_RECORD_CONVERT_TEXT = "pref_setting_convert_text";

    // Others Settings
    public static final String CATEGORY_SMART = "pref_category_smart";
    public static final String SETTING_RECORD_SMART_GENERATION = "pref_setting_smart_generation";
    public static final String SETTING_RECORD_PICTURE_RECOMMENDATION = "pref_setting_record_picture_recommendation";

    public static final String SETTING_RECORD_FEEDBACK = "pref_record_feed_back";
    public static final String SETTING_RECORD_ABOUT = "pref_record_about";

    public static final String SETTING_RECORD_COLLECTION_KEY = "privacy_policy_settings_collection_key";

    public static final String SETTING_RECORD_SHARE_KEY = "privacy_policy_settings_share_key";

    public static final String BOTTOM_RECOMMEND_CARD_PREFERENCE = "bottom_recommend_card_preference";

    public static final String VERSION_CODE = "versionCode";
    public static final int SYNC_STATE_SUCCESS_OF_SHOW = 0x101;
    public static final int SYNC_STATE_SUCCESS_OF_JUMP = 0x102;
    public static final int QUERY_RECORD_FORMAT = 0x103;
    public static final int QUERY_RECORD_MODE = 0x104;
    public static final int QUERY_THIRD_RECORD_MODE = 0x105;

    public static final int QUERY_SMART_NAME_MODE = 0x106;
    public static final int QUERY_SPEECH_LANGUAGE_MODE = 0x107;
    public static final int QUERY_SUPPORT_LANGUAGE = 0x108;
    public static final int SHOW_END_RED_DOT_MODE = 1;

    private final CloudKitInterface mCloudKitApi = KoinInterfaceHelper.INSTANCE.getCloudKitApi();
    private final CloudTipManagerAction mCloudTipManagerAction = KoinInterfaceHelper.INSTANCE.getCloudTipManagerAction();
    private final SmartNameAction mSmartNameAction = KoinInterfaceHelper.INSTANCE.getSmartNameAction();
    private final FeedBackInterface mFeedBackApi = KoinInterfaceHelper.INSTANCE.getFeedbackApi();
    private final SettingInterface mSettingApi = KoinInterfaceHelper.INSTANCE.getSettingApi();
    private final RecordInterface mRecordInterface = KoinInterfaceHelper.INSTANCE.getRecorderApi();
    private final ConvertSupportAction mConvertSupportAction = KoinInterfaceHelper.INSTANCE.getConvertSupportAction();
    private final AIAsrManagerAction mAiAsrManagerAction = KoinInterfaceHelper.INSTANCE.getAiAsrManagerAction();
    private final RecorderServiceInterface mRecorderViewModelApi = KoinInterfaceHelper.INSTANCE.getRecorderViewModelApi();

    private COUIPreferenceCategory mPreferenceCategorySmart;
    private SmartNamePreference mSmartGenerationPreference;
    private COUISwitchPreference mSettingRecordPictureRecommendation;
    private COUIJumpPreference mSettingRecordSync;
    // 外销 音频格式选择
    private COUIJumpPreference mPreferenceJumpAudioFormat;
    private COUIJumpPreference mPreferenceJumpAudioMode; // 录制模式选择
    private COUIJumpPreference mPreferenceJumpSpeechLanguage;       // 音频语言选择
    private COUIPreferenceCategory mPreferenceCategoryTranscribe;   // 分类-转写

    /*外销格式选择弹窗*/
    private SelectRecordAudioFormatDialog mOpAudioFormatDialog;
    private SelectLanguageDialog mSelectLanguageDialog;
    private RecordModeDialog mRecordModeDialog; // 录制模式选择弹窗
    private ExecutorService mExecutor = Executors.newSingleThreadExecutor();
    private COUIToolbar mToolbar = null;
    private boolean mSupportMultiRecordMode; // 是否支持多种录制模式
    private int mAudioModeValue; // 选择的录制模式
    private boolean mShowAudioModeRedDot; // 是否显示录制模式小红点

    private IUnifiedSummaryCallBack mUnifiedSummaryManager;
    private boolean mSupportSmartName;
    private AlertDialog mFilePermissionDialog = null;
    private boolean mSmartFilePermissionCheck = false;

    private IAsrPluginCallBack mAsrPluginManager;

    private COUIRecommendedPreference mBottomRecommendPreference;
    private String mNewRecordForThirdAppName = "";

    private List<String> mSpLanguageList = null;

    private final Handler sHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            switch (msg.what) {
                case SYNC_STATE_SUCCESS_OF_SHOW:
                    setNewSettingSync(mSwitchState);
                    break;
                case SYNC_STATE_SUCCESS_OF_JUMP:
                    Context context = getContext();
                    if (context != null) {
                        mCloudTipManagerAction.launchCloudSettingPage(context);
                    }
                    break;
                case QUERY_RECORD_FORMAT:
                    updateAudioFormatValue(msg.arg1);
                    break;
                case QUERY_RECORD_MODE:
                    if (mPreferenceJumpAudioMode != null) {
                        mPreferenceJumpAudioMode.setAssignment(RecordModeDialog.getModeName(mAudioModeValue));
                        if (mShowAudioModeRedDot) {
                            mPreferenceJumpAudioMode.setEndRedDotMode(SHOW_END_RED_DOT_MODE);
                            mPreferenceJumpAudioMode.showEndRedDot();
                        } else {
                            mPreferenceJumpAudioMode.dismissEndRedDot();
                        }
                    }
                    break;
                case QUERY_THIRD_RECORD_MODE:
                    // audioMonitor是支持摘要版本，app名称变更
                    if (mBottomRecommendPreference != null && getActivity() != null) {
                        ThirdRecordResultBean resultBean = (ThirdRecordResultBean) msg.obj;
                        mNewRecordForThirdAppName = ThirdAppRecordCheckManager.getTitle(getActivity(), resultBean);
                        initBottomRecommendCard();
                    }
                    break;
                case QUERY_SMART_NAME_MODE:
                    if (mSmartGenerationPreference != null && getActivity() != null) {
                        boolean isOpen = mSmartNameAction.isSmartNameSwitchOpen(getActivity());
                        boolean supportSmartName = (boolean) msg.obj;
                        mSmartGenerationPreference.setChecked(isOpen);
                        boolean isShow = isOpen && !isFromOtherApp();
                        mSmartGenerationPreference.setVisible(supportSmartName || isShow);
                        updateShowSmartCategory();
                    }
                    break;
                case QUERY_SPEECH_LANGUAGE_MODE:
                    if (mPreferenceJumpSpeechLanguage != null) {
                        boolean asrSupportAndDownload = (boolean) msg.obj;
                        mPreferenceJumpSpeechLanguage.setVisible(asrSupportAndDownload && !isFromOtherApp());
                        updateShowTranscribeCategory();
                    }
                    break;
                case QUERY_SUPPORT_LANGUAGE:
                    handleMessageQuerySupportLanguage();
                    break;
                default:
                    DebugUtil.i(TAG, "unknown msg.what " + msg.what);
                    break;
            }
        }
    };

    private void handleMessageQuerySupportLanguage() {
        Activity activity = getActivity();
        if (activity == null || mRecorderViewModelApi == null) {
            return;
        }
        // 判断有没有网络
        if (NetworkUtils.isNetworkInvalid(activity)) {
            DebugUtil.d(TAG, "handleMessageQuerySupportLanguage, not network");
            return;
        }
        mRecorderViewModelApi.getSupportLanguageListFromServer(languageCodes -> {
            if (languageCodes == null) {
                return;
            }
            mSpLanguageList = languageCodes;
            DebugUtil.d(TAG, "handleMessageQuerySupportLanguage, languageCodes=" + languageCodes);
            String languageCode = mRecorderViewModelApi.getCurSelectedLanguage();
            if (!languageCodes.contains(languageCode)) {
                String asrDefaultLanguageCode = LanguageUtil.getAsrDefaultSupportLanguageWithLocal(languageCodes);
                mRecorderViewModelApi.setCurSelectedLanguage(asrDefaultLanguageCode);
                languageCode = asrDefaultLanguageCode;
            }
            String finalLanguageCode = languageCode;
            activity.runOnUiThread(() -> {
                if (activity.isFinishing() || activity.isDestroyed()) {
                    return;
                }
                updateLanguagePreferenceSummary(finalLanguageCode);
            });
            if (mSelectLanguageDialog == null) {
                DebugUtil.w(TAG, "handleMessageQuerySupportLanguage, mSelectLanguageDialog is null");
                return;
            }
            mSelectLanguageDialog.setSpLanguageSetNullOrEmpty(false);
            // 若已弹出语言选择对话框，则判断是否需要更新
            boolean showing = mSelectLanguageDialog.isShowing();
            boolean isLanguageListChange = mSelectLanguageDialog.compareLanguageListChange(languageCodes);
            boolean isLanguageSelectedChange = mSelectLanguageDialog.compareSelectedLanguageChange(languageCode);
            DebugUtil.d(TAG, "handleMessageQuerySupportLanguage, isShowing=" + showing + ", isLanguageListChange="
                    + isLanguageListChange + ", isLanguageSelectedChange=" + isLanguageSelectedChange);
            if (showing && (isLanguageListChange || isLanguageSelectedChange)) {
                mSelectLanguageDialog.showDialog(languageCode);
            }
        });
    }

    private int mSwitchState = -1;

    private ActivityResultLauncher<String[]> requestPermission;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = super.onCreateView(inflater, container, savedInstanceState);
        initActionBar(view);
        if (getListView() != null) {
            getListView().setItemAnimator(null);
            getListView().setForceDarkAllowed(false);
        }
        addPreferencesFromResource(R.xml.settings);
        if (mAiAsrManagerAction != null) {
            mAsrPluginManager = mAiAsrManagerAction.newAsrPluginManager();
        }
        if (getActivity() != null && mSmartNameAction != null) {
            mUnifiedSummaryManager = mSmartNameAction.newUnifiedSummaryManager();
        }

        mSettingRecordSync = findPreference(SETTING_RECORD_SYNC);
        if (mSettingRecordSync != null) {
            boolean isVisible = mCloudKitApi != null && mCloudKitApi.isSupportCloudArea();
            mSettingRecordSync.setVisible(isVisible);
            mSettingRecordSync.setOnPreferenceClickListener(this);
        }

        COUIJumpPreference settingRecordAbout = findPreference(SETTING_RECORD_ABOUT);
        if (settingRecordAbout != null) {
            settingRecordAbout.setOnPreferenceClickListener(this);
        }
        //收集个人信息明示清单
        COUIJumpPreference settingCollection = findPreference(SETTING_RECORD_COLLECTION_KEY);
        if (settingCollection != null && !BaseUtil.isEXP()) {
            settingCollection.setVisible(true);
            settingCollection.setOnPreferenceClickListener(this);
        }
        //三方信息共享清单
        COUIJumpPreference settingShare = findPreference(SETTING_RECORD_SHARE_KEY);
        if (settingShare != null) {
            boolean isDomestic = !BaseUtil.isEXP();
            if (isDomestic) {
                settingShare.setOnPreferenceClickListener(this);
            }
            settingShare.setVisible(isDomestic);
        }

        boolean isDeviceProtectedStorage = false;
        if (getActivity() != null) {
            isDeviceProtectedStorage = getActivity().isDeviceProtectedStorage();
        }
        DebugUtil.i(TAG, "isDeviceProtectedStorage: " + isDeviceProtectedStorage);
        // EXP version soundrecorder is local in data/user_de/0/com.coloros.soundrecorder, is devicesProtectedStorage, start FeedbackActivity will crash because of java.lang.IllegalArgumentException: WebView cannot be used with device protected storage
        // neixiao version soundrecorder is local in data/user/0/com.coloros.soundrecorder, is credentialProtectedStorage , start FeedbackAcitity normally
        if (mFeedBackApi == null
                || (!LocalCountryData.INSTANCE.getJson(getContext()).contains(Locale.getDefault().getCountry()) && BaseUtil.isEXP())) {
            COUIJumpPreference categoryFeedback = findPreference(SETTING_RECORD_FEEDBACK);
            if (categoryFeedback != null) {
                categoryFeedback.setVisible(false);
            }
        } else {
            COUIJumpPreference feedBack = findPreference(SETTING_RECORD_FEEDBACK);
            if (feedBack != null) {
                feedBack.setOnPreferenceClickListener(this);
            }
        }
        COUIPreference versionCode = findPreference(VERSION_CODE);
        if (versionCode != null) {
            versionCode.setTitle(BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.version_num));
            versionCode.setSummary(AppUtil.getAppVersionName());
        }
        initOpExpAudioFormatView();
        initAudioMode();
        initPrivacyPolicy();
        initSmartGeneration();
        initSpeechLanguage();
        initBottomRecommendCard();

        querySupportLanguage("onCreateView");
        return view;
    }

    private void initBottomRecommendCard() {
        mBottomRecommendPreference = findPreference(BOTTOM_RECOMMEND_CARD_PREFERENCE);
        if (mBottomRecommendPreference == null) {
            return;
        }
        ArrayList<COUIRecommendedPreference.RecommendedEntity> recommendedEntities = new ArrayList<>();

        /*三方应用通话录音View，只在内销机型并且集成了三方通话录音APK的情况下才会显示*/
        if (ThirdAppRecordCheckManager.isSupportTripartiteAudioMonitor()) {
            String title = !mNewRecordForThirdAppName.isEmpty() ? mNewRecordForThirdAppName
                    : BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.record_audio_for_third_app_v2);
            DebugUtil.d(TAG, "initBottomRecommendCard, title:" + title);
            // AI 语音摘记
            COUIRecommendedPreference.RecommendedEntity recordForThirdApp =
                    new COUIRecommendedPreference.RecommendedEntity(title, view -> ThirdAppRecordCheckManager.jumpToThirdAppSetting(getContext()));
            recommendedEntities.add(recordForThirdApp);
        }
        mBottomRecommendPreference.setData(recommendedEntities);
    }

    private int getCurrentTaskId() {
        if (getActivity() == null) {
            return -1;
        }
        return getActivity().getTaskId();
    }

    private boolean isFromOtherApp() {
        return AppTaskUtil.isFromOtherApp(getCurrentTaskId());
    }

    private void initSmartGeneration() {
        mSmartGenerationPreference = findPreference(SETTING_RECORD_SMART_GENERATION);
        if (getActivity() != null && mSmartGenerationPreference != null) {
            boolean isOpen = mSmartNameAction != null && mSmartNameAction.isSmartNameSwitchOpen(getActivity());
            //初始化开关状态，否则在isOpen为true时，大屏模式下会闪一下，执行打开开关的动画
            mSmartGenerationPreference.setChecked(isOpen);
            mSupportSmartName = AppTaskUtil.INSTANCE.isSupportSmartName();
            boolean isShow = isOpen && !isFromOtherApp();
            DebugUtil.d(TAG, "initSmartTitle, isFromOtherApp:" + isFromOtherApp() + ", "
                    + ", supportSmartName:" + mSupportSmartName);
            mSmartGenerationPreference.setVisible(mSupportSmartName || isShow);
            updateShowSmartCategory();
            mSmartGenerationPreference.setOnClick(v -> switchSmartGeneration());
        }
    }

    /**
     * 智能分类是否显示更新
     */
    private void updateShowSmartCategory() {
        mPreferenceCategorySmart = findPreference(CATEGORY_SMART);
        if (mPreferenceCategorySmart == null) {
            return;
        }
        // 判断mPreferenceCategorySmart所有子项是否有可见项，若没有可见项，则不显示智能分类
        boolean hasChildVisible = false;
        for (int i = 0; i < mPreferenceCategorySmart.getPreferenceCount(); i++) {
            if (mPreferenceCategorySmart.getPreference(i).isVisible()) {
                hasChildVisible = true;
                break;
            }
        }
        mPreferenceCategorySmart.setVisible(hasChildVisible);
    }

    /**
     * 转写分类是否显示更新
     */
    private void updateShowTranscribeCategory() {
        mPreferenceCategoryTranscribe = findPreference(CATEGORY_TRANSCRIBE);
        if (mPreferenceCategoryTranscribe == null) {
            return;
        }
        // 判断mPreferenceCategoryTranscribe所有子项是否有可见项，若没有可见项，则不显示转写分类
        boolean hasChildVisible = false;
        for (int i = 0; i < mPreferenceCategoryTranscribe.getPreferenceCount(); i++) {
            if (mPreferenceCategoryTranscribe.getPreference(i).isVisible()) {
                hasChildVisible = true;
                break;
            }
        }
        mPreferenceCategoryTranscribe.setVisible(hasChildVisible);
    }

    private void initSpeechLanguage() {
        DebugUtil.d(TAG, "initSpeechLanguage");
        mPreferenceJumpSpeechLanguage = findPreference(SETTING_SPEECH_LANGUAGE);
        Activity activity = getActivity();
        if (activity == null || mPreferenceJumpSpeechLanguage == null) {
            return;
        }
        boolean asrPluginSupportAndDownload = mAsrPluginManager != null && mAsrPluginManager.isAsrPluginSupportAndDownload(activity);
        DebugUtil.d(TAG, "initSpeechLanguage asrPluginSupportAndDownload=" + asrPluginSupportAndDownload + " isFromOtherApp=" + isFromOtherApp());
        mPreferenceJumpSpeechLanguage.setVisible(asrPluginSupportAndDownload && !isFromOtherApp());
        updateShowTranscribeCategory();

        // 首先判断sp是否有语种列表，若没有，优先拿本地语种配置
        if (mRecorderViewModelApi != null) {
            mSpLanguageList = mRecorderViewModelApi.getLangListFromCatchOrSp();
        }
        if (mSpLanguageList == null || mSpLanguageList.isEmpty()) {
            DebugUtil.d(TAG, "initSpeechLanguage getSupportLanguageList languageCodes is null or empty");
            String languageCode = LanguageUtil.getAsrDefaultLanguage();
            if (mRecorderViewModelApi != null) {
                languageCode = mRecorderViewModelApi.getCurSelectedLanguage();
            }
            updateLanguagePreferenceSummary(languageCode);
            mPreferenceJumpSpeechLanguage.setOnPreferenceClickListener(this);
        } else {
            String languageCode = LanguageUtil.getAsrDefaultLanguage();
            if (mRecorderViewModelApi != null) {
                languageCode = mRecorderViewModelApi.getCurSelectedLanguage();
            }
            DebugUtil.d(TAG, "initSpeechLanguage size=" + mSpLanguageList.size() + " languageCode=" + languageCode);
            // 判断当前语言是否支持，若不支持，则切换为默认语言
            if (!mSpLanguageList.contains(languageCode)) {
                String asrDefaultLanguageCode = LanguageUtil.getAsrDefaultSupportLanguageWithLocal(mSpLanguageList);
                if (mRecorderViewModelApi != null) {
                    mRecorderViewModelApi.setCurSelectedLanguage(asrDefaultLanguageCode);
                }
                languageCode = asrDefaultLanguageCode;
            }
            String finalLanguageCode = languageCode;
            activity.runOnUiThread(() -> {
                if (activity.isFinishing() || activity.isDestroyed()) {
                    return;
                }
                updateLanguagePreferenceSummary(finalLanguageCode);
            });
            mPreferenceJumpSpeechLanguage.setOnPreferenceClickListener(this);
        }
    }

    private void initActionBar(View view) {
        AppCompatActivity activity = (AppCompatActivity) getActivity();
        if (activity == null) {
            return;
        }
        mToolbar = view.findViewById(R.id.toolbar);
        mToolbar.setIsTitleCenterStyle(false);
        activity.setSupportActionBar(mToolbar);
        ActionBar actionBar = activity.getSupportActionBar();
        if (actionBar != null) {
            actionBar.setHomeButtonEnabled(true);
            actionBar.setDisplayHomeAsUpEnabled(true);
            actionBar.setTitle(BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.app_name_settings));
        }
        initiateWindowInsets(activity);
    }

    private void initiateWindowInsets(Activity activity) {
        View root = activity.findViewById(R.id.root_layout);
        if (root == null) {
            return;
        }
        WindowCompat.setDecorFitsSystemWindows(activity.getWindow(), false);
        RootViewPersistentInsetsCallback callback = new RootViewPersistentInsetsCallback() {

            @Override
            public void onApplyInsets(@NotNull View v, @NotNull WindowInsetsCompat insets) {
                super.onApplyInsets(v, insets);
                DebugUtil.i(TAG, "onApplyInsets");
                TaskBarUtil.setNavigationColorOnSupportTaskBar(
                        insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.navigationBars()).bottom,
                        activity,
                        ((BaseActivity) activity).navigationBarColor()
                );
            }
        };
        ViewCompat.setOnApplyWindowInsetsListener(root, callback);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        if (FollowHandPanelUtils.isAddOnSupportFollowPanel()) {
            ViewUtils.doOnLayoutChange(view, (view1, rect, rect2) -> {
                checkIsFlexible();
                return Unit.INSTANCE;
            });
        }
    }

    /**
     * 使标题在面板下能够横向居中
     */
    private void checkIsFlexible() {
        DebugUtil.d(TAG, "checkIsFlexible");
        if (mToolbar == null) {
            return;
        }
        boolean isFlexible = FollowHandPanelUtils.isFlexibleActivitySuitable(getResources().getConfiguration());
        if (isFlexible) {
            //  浮窗面板，显示X
            mToolbar.setNavigationIcon(com.support.snackbar.R.drawable.coui_menu_ic_cancel);
        } else {
            // 常规屏，显示返回箭头
            mToolbar.setNavigationIcon(com.support.appcompat.R.drawable.coui_back_arrow);
        }
    }

    private void cloudSynState(int msgWhat) {
        DebugUtil.e(TAG, "cloudSynState what: " + msgWhat);
        if (mCloudKitApi == null || !mCloudKitApi.isSupportCloudArea()) {
            if (mSettingRecordSync != null) {
                mSettingRecordSync.setVisible(false);
            }
            DebugUtil.e(TAG, "current area not support cloud return");
            return;
        }
        if (mExecutor != null) {
            mExecutor.execute(() -> {
                mSwitchState = mCloudKitApi.queryCloudSwitchState(true);
                sHandler.sendEmptyMessage(msgWhat);
            });
        }
    }

    private void queryRecordFormat() {
        if (FunctionOption.IS_SUPPORT_WAV_AND_AAC && (mExecutor != null)) {
            mExecutor.execute(() -> {
                Message msg = sHandler.obtainMessage(QUERY_RECORD_FORMAT);
                msg.arg1 = OPSettingUtils.getOPAudioFormat(BaseApplication.getAppContext());
                sHandler.sendMessage(msg);
            });
        }
    }

    private void queryRecordMode() {
        if (mSupportMultiRecordMode && (mExecutor != null)) {
            mExecutor.execute(() -> {
                mShowAudioModeRedDot = RecordModeDialog.isNeedShowRecordModeRedDot();
                mAudioModeValue = RecordModeUtil.getModeValue();
                sHandler.sendEmptyMessage(QUERY_RECORD_MODE);
            });
        }
    }

    private void queryPictureRecommendSwitch() {
        if (FunctionOption.IS_SUPPORT_PHOTO_RECOMMEND && (mExecutor != null)) {
            mExecutor.execute(() -> {
                boolean isOpen = FunctionOption.isSupportPhotoMarkRecommend();
                setPictureRecommendation(isOpen, true);
            });
        }
    }

    private void initPrivacyPolicy() {
        String privacyPolicyKey = BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.privacy_policy_settings_key);
        COUIJumpPreference privacyPolicy = findPreference(privacyPolicyKey);
        if (privacyPolicy != null) {
            //外销需要显示用户协议界面，所以不再判断是否为外销，内外销都显示隐私
            privacyPolicy.setOnPreferenceClickListener(preference -> {
                BuryingPoint.addRecordSettingIconClick(RecorderUserAction.VALUE_NAME_PRIVACY,
                        RecorderUserAction.VALUE_OPTION_RECORD_TYPE_DEFAULT);
                Context context = getContext();
                if (context != null) {
                    Intent intent = new Intent(context, PrivacyPolicyActivity.class);
                    FollowHandPanelUtils.startActivity(context, intent);
                }
                return false;
            });
        }
    }

    /**
     * 外销 录音格式选择入口View
     */
    private void initOpExpAudioFormatView() {
        if (!FunctionOption.IS_SUPPORT_WAV_AND_AAC) {
            return;
        }
        mPreferenceJumpAudioFormat = findPreference(JUMP_OP_EXP_AUDIO_FORMAT);
        if (mPreferenceJumpAudioFormat != null) {
            mPreferenceJumpAudioFormat.setVisible(true);
            mPreferenceJumpAudioFormat.setOnPreferenceClickListener(this);
        }
    }

    private void initAudioMode() {
        mSupportMultiRecordMode = RecordModeUtil.isSupportMultiRecordMode(BaseApplication.getAppContext());
        mPreferenceJumpAudioMode = findPreference(JUMP_OP_EXP_AUDIO_MODE);
        if (mPreferenceJumpAudioMode != null) {
            mPreferenceJumpAudioMode.setVisible(mSupportMultiRecordMode);
            mPreferenceJumpAudioMode.setOnPreferenceClickListener(this);
        }
    }

    /**
     * 更新当前选择音频格式view
     */
    private void updateAudioFormatValue(int value) {
        if (mPreferenceJumpAudioFormat == null) {
            return;
        }
        String formatStr = "";
        switch (value) {
            case RecorderConstant.RECORDER_AUDIO_FORMAT_AAC_ADTS:
                formatStr = SelectRecordAudioFormatDialog.AAC_SHOW_STR;
                break;
            case RecorderConstant.RECORDER_AUDIO_FORMAT_WAV:
                formatStr = SelectRecordAudioFormatDialog.WAV_SHOW_STR;
                break;
            case RecorderConstant.RECORDER_AUDIO_FORMAT_MP3:
                formatStr = SelectRecordAudioFormatDialog.MP3_SHOW_STR;
                break;
        }
        mPreferenceJumpAudioFormat.setAssignment(formatStr);
    }

    public void onActivityTopResumed() {
        DebugUtil.v(TAG, "onActivityTopResumed");
        refreshDataWhenResume();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        DebugUtil.e(TAG, "onDestroy");
        // release op-exp select audio-format dialog
        if (mOpAudioFormatDialog != null) {
            mOpAudioFormatDialog.release();
            mOpAudioFormatDialog = null;
        }
        if (mSelectLanguageDialog != null) {
            mSelectLanguageDialog.release();
            mSelectLanguageDialog = null;
        }
        if (mRecordModeDialog != null) {
            mRecordModeDialog.release();
            mRecordModeDialog = null;
        }
        if (mUnifiedSummaryManager != null) {
            mUnifiedSummaryManager.releaseAllDialog();
            mUnifiedSummaryManager = null;
        }
        if (mExecutor != null) {
            mExecutor.shutdown();
            mExecutor = null;
        }
        dismissFilePermissionDialog();
        sHandler.removeCallbacksAndMessages(null);
    }

    private void dismissFilePermissionDialog() {
        if (mFilePermissionDialog != null && mFilePermissionDialog.isShowing()) {
            mFilePermissionDialog.dismiss();
            mFilePermissionDialog = null;
        }
        sHandler.removeCallbacksAndMessages(null);
    }

    @Override
    public void onResume() {
        DebugUtil.v(TAG, "onResume");
        super.onResume();
        refreshDataWhenResume();
    }

    private void refreshDataWhenResume() {
        queryRecordFormat();
        cloudSynState(SYNC_STATE_SUCCESS_OF_SHOW);
        queryPictureRecommendSwitch();
        queryRecordMode();
        queryThirdAppRecordMode();
        if (mSmartFilePermissionCheck) {
            checkRestoreSmartName();
        } else {
            querySmartNameOpenMode();
        }
        querySpeechLanguageMode();
    }

    private void checkRestoreSmartName() {
        DebugUtil.d(TAG, "checkRestoreSmartName");
        if (PermissionUtils.hasAllFilePermission()) {
            showSmartAiunitDialog();
        }
        mSmartFilePermissionCheck = false;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            Activity activity = getActivity();
            if (activity != null) {
                activity.finish();
            }
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    @Override
    public boolean onPreferenceChange(Preference preference, Object newValue) {
        DebugUtil.d(TAG, "onPreferenceChange, key:" + preference.getKey() + " value:" + newValue);
        return false;
    }

    @Override
    public boolean onPreferenceClick(Preference preference) {
        if (null != preference) {
            String key = preference.getKey();
            switch (key) {
                case SETTING_RECORD_SYNC: {
                    cloudSynState(SYNC_STATE_SUCCESS_OF_JUMP);
                    break;
                }
                case SETTING_RECORD_FEEDBACK: {
                    BuryingPoint.addRecordSettingIconClick(RecorderUserAction.VALUE_NAME_HELP, RecorderUserAction.VALUE_OPTION_RECORD_TYPE_DEFAULT);
                    if (getActivity() != null) {
                        String id = OpenIdUtils.INSTANCE.getDUID();
                        if (id.isEmpty()) {
                            DebugUtil.e(TAG, "feedback id is empty!");
                        }
                        mFeedBackApi.launchFeedBack(getActivity(), id,
                                COUIContextUtil.getAttrColor(getActivity(), com.support.appcompat.R.attr.couiColorPrimary));
                    }
                    break;
                }
                case SETTING_RECORD_ABOUT: {
                    BuryingPoint.addRecordSettingIconClick(RecorderUserAction.VALUE_NAME_ABOUT_RECORD,
                            RecorderUserAction.VALUE_OPTION_RECORD_TYPE_DEFAULT);
                    Context context = getContext();
                    if (context != null) {
                        Intent intent = new Intent(context, RecordAboutActivity.class);
                        FollowHandPanelUtils.startActivity(context, intent);
                    }
                    break;
                }
                case SETTING_RECORD_COLLECTION_KEY: {
                    BuryingPoint.addRecordSettingIconClick(RecorderUserAction.VALUE_NAME_PERSONAL_INFO,
                            RecorderUserAction.VALUE_OPTION_RECORD_TYPE_DEFAULT);
                    mSettingApi.launchCollectionInfo(getActivity(),
                            com.soundrecorder.common.R.string.privacy_policy_settings_collection_key);
                    break;
                }
                case SETTING_RECORD_SHARE_KEY: {
                    mSettingApi.launchRecordPrivacy(getActivity(), com.soundrecorder.common.R.string.privacy_policy_settings_share_key);
                    break;
                }
                case SETTING_RECORD_PICTURE_RECOMMENDATION:
                    switchPictureRecommendation();
                    break;
                case JUMP_OP_EXP_AUDIO_FORMAT:
                    if (ClickUtils.isFastDoubleClick(DURATION_500)) {
                        DebugUtil.i(TAG, "audio format fast double click!");
                        return false;
                    }
                    showSelectAudioFormatDialog();
                    break;
                case JUMP_OP_EXP_AUDIO_MODE:
                    if (ClickUtils.isFastDoubleClick(DURATION_500)) {
                        DebugUtil.i(TAG, "audio mode fast double click!");
                        return false;
                    }
                    showSelectAudioModeDialog();
                    break;
                case JUMP_THIRD_APP_RECORD_SETTING:
                    ThirdAppRecordCheckManager.jumpToThirdAppSetting(getContext());
                    break;
                case SETTING_SPEECH_LANGUAGE:
                    if (ClickUtils.isFastDoubleClick(DURATION_500)) {
                        DebugUtil.i(TAG, "audio mode fast double click!");
                        return false;
                    }
                    showSelectLanguageDialog();
                    break;
                default:
                    break;
            }
        }
        return false;
    }

    private void showPermissionAllFileDialog() {
        if (getActivity() == null) {
            return;
        }
        mFilePermissionDialog = PermissionDialogUtils.showPermissionAllFileAccessDialog(getActivity(),
                new PermissionDialogUtils.PermissionDialogListener() {
                    @Override
                    public void dialogPermissionType(int dialogPermissionType) {
                        DebugUtil.d(TAG, "dialogPermissionType:" + dialogPermissionType);
                    }

                    @Override
                    public void onBackPress(int alertType) {
                        DebugUtil.d(TAG, "onBackPress, alertType:" + alertType);
                    }

                    @Override
                    public void onClick(int alertType, boolean isOk, @Nullable ArrayList<String> permissions) {
                        if (isOk) {
                            mSmartFilePermissionCheck = true;
                            PermissionUtils.goToAppAllFileAccessConfigurePermissions(getActivity());
                        }
                        dismissFilePermissionDialog();
                    }
                });
    }

    private void switchSmartGeneration() {
        DebugUtil.d(TAG, "switchSmartGeneration");
        if (ClickUtils.isFastDoubleClick(DURATION_500) || getActivity() == null) {
            return;
        }

        boolean isOpen = mSmartNameAction.isSmartNameSwitchOpen(getActivity());
        if (isOpen) {
            // 用户要关闭开关，直接关闭
            mSmartGenerationPreference.setChecked(false);
            mSmartNameAction.setSmartNameSwitchStatus(getActivity(), false, true);
        } else {
            // 用户要打开开关，先检查是否有智能生成功能的权限
            if (!hasFuncTypePermission(TYPE_PERMISSION_SMART_SHORTHAND)) {
                showSmartGenerationPrivacyDialog();
                return;
            }
            mSmartGenerationPreference.setChecked(true);
            mSmartNameAction.setSmartNameSwitchStatus(getActivity(), true, true);
            continueSmartGenerationSetup();
        }
    }

    /**
     * 显示智能生成功能隐私协议对话框
     */
    private void showSmartGenerationPrivacyDialog() {
        final Activity activity = getActivity();
        if (!(activity instanceof AppCompatActivity)) {
            return;
        }

        PrivacyPolicyInterface privacyApi = KoinInterfaceHelper.INSTANCE.getPrivacyPolicyInterface();
        if (privacyApi == null) {
            return;
        }
        final IPrivacyPolicyDelegate privacyDelegate = privacyApi.newPrivacyPolicyDelegate(
                (AppCompatActivity) activity,
                POLICY_TYPE_COMMON,
                this
        );

        privacyDelegate.resumeShowDialog(TYPE_PERMISSION_SMART_SHORTHAND, true, null);
    }

    private void continueSmartGenerationSetup() {
        if (!PermissionUtils.hasAllFilePermission()) {
            showPermissionAllFileDialog();
            return;
        }
        showSmartAiunitDialog();
    }

    private void showSmartAiunitDialog() {
        if (getActivity() == null || mUnifiedSummaryManager == null) {
            return;
        }
        mSmartFilePermissionCheck = false;
        mUnifiedSummaryManager.showAiUnitPluginsDialog(getActivity(), new SettingPluginDownloadCallback(this), true, false);
    }

    private void setNewSettingSync(int switchState) {
        switch (switchState) {
            case CloudSwitchState.OPEN_ONLY_WIFI:
                mSettingRecordSync.setVisible(true);
                mSettingRecordSync.setSummary(BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.sync_is_wlan));
                mSettingRecordSync.setAssignment(BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.has_been_open_of_settings));
                break;
            case CloudSwitchState.OPEN_ALL:
                mSettingRecordSync.setVisible(true);
                mSettingRecordSync.setSummary(BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.sync_is_unlimited));
                mSettingRecordSync.setAssignment(BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.has_been_open_of_settings));
                break;
            default:
                if ((switchState == CloudSwitchState.NOT_LOGIN) && (BaseUtil.isExpRSA4())) {
                    //外销 RSA4.0 +未登录，不显示卡片
                    mSettingRecordSync.setVisible(false);
                    break;
                }
                mSettingRecordSync.setVisible(true);
                mSettingRecordSync.setSummary("");
                mSettingRecordSync.setAssignment(BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.did_not_open_of_settings));
                break;
        }
    }

    /**
     * 录制音频格式选择弹窗，不重建(交互通过)
     */
    private void showSelectAudioFormatDialog() {
        Context context = getContext();
        if (context == null) {
            return;
        }
        if (mOpAudioFormatDialog == null) {
            mOpAudioFormatDialog = new SelectRecordAudioFormatDialog(context);
        }
        mOpAudioFormatDialog.setMDialogItemListener(audioFormat -> {
            // 更新页面UI
            updateAudioFormatValue(audioFormat);
            // 保存到SP
            OPSettingUtils.INSTANCE.saveOPAudioFormat(context, audioFormat);
            // 埋点
            burySelectAudioFormatType(audioFormat);

        });
        mOpAudioFormatDialog.showDialog(OPSettingUtils.INSTANCE.getOPAudioFormat(context));
    }

    private void showSelectLanguageDialog() {
        Activity activity = getActivity();
        if (activity == null) {
            return;
        }
        boolean isSpLanguageSetNullOrEmpty = mSpLanguageList == null || mSpLanguageList.isEmpty();
        if (mSelectLanguageDialog == null) {
            mSelectLanguageDialog = new SelectLanguageDialog(activity, isSpLanguageSetNullOrEmpty);
            mSelectLanguageDialog.setDialogItemListener(languageCode -> {
                DebugUtil.d(TAG, "showSelectLanguageDialog Selected language code: " + languageCode);
                // 保存选择的语言到RecorderViewModel并更新SP
                if (mRecorderViewModelApi != null) {
                    mRecorderViewModelApi.setCurSelectedLanguage(languageCode);
                }
                // 更新页面UI
                updateLanguagePreferenceSummary(languageCode);
            });
        } else {
            mSelectLanguageDialog.setSpLanguageSetNullOrEmpty(isSpLanguageSetNullOrEmpty);
        }

        // 获取当前选择的语言，若没有，内销默认为中文，外销默认为英文
        String defaultLanguage = LanguageUtil.getAsrDefaultLanguage();
        String currentLanguage = "";
        if (mRecorderViewModelApi != null) {
            currentLanguage = mRecorderViewModelApi.getCurSelectedLanguage();
        }
        if (TextUtils.isEmpty(currentLanguage)) {
            currentLanguage = defaultLanguage;
        }
        DebugUtil.d(TAG, "showSelectLanguageDialog currentLanguage=" + currentLanguage);
        mSelectLanguageDialog.showDialog(currentLanguage);
    }

    private void updateLanguagePreferenceSummary(String languageCode) {
        Context context = getContext();
        if (context == null) {
            return;
        }
        if (mPreferenceJumpSpeechLanguage != null) {
            String languageName = LanguageUtil.getLanguageDisplayName(context, languageCode);
            if (languageName == null) {
                String asrDefaultLanguageCode = LanguageUtil.getAsrDefaultLanguage();
                if (mRecorderViewModelApi != null) {
                    mRecorderViewModelApi.setCurSelectedLanguage(asrDefaultLanguageCode);
                }
                languageName = LanguageUtil.getLanguageDisplayName(context, asrDefaultLanguageCode);
            }
            DebugUtil.d(TAG, "updateLanguagePreferenceSummary languageCode=" + languageCode + " languageName=" + languageName);
            mPreferenceJumpSpeechLanguage.setAssignment(languageName);
        }
    }

    private void showSelectAudioModeDialog() {
        Context context = getContext();
        if (context == null) {
            return;
        }
        if (mRecordModeDialog == null) {
            mRecordModeDialog = new RecordModeDialog(context);
        }
        mRecordModeDialog.setMDialogItemListener((mode, modeName) -> {
            // 更新页面UI
            mPreferenceJumpAudioMode.setAssignment(modeName);
            mAudioModeValue = mode;
        });
        mRecordModeDialog.showDialog(mAudioModeValue);
        // 点击弹窗后，小红点消失
        burySelectAudioModeDialogMode(mAudioModeValue);
        if (mShowAudioModeRedDot) {
            mShowAudioModeRedDot = false;
            mPreferenceJumpAudioMode.dismissEndRedDot();
            mExecutor.execute(() -> {
                RecordModeDialog.setRecordModeRedDotShowed();
                // 引导弹窗设置已显示，避免分屏进入录音，引导弹窗未显示，又进入了设置点击显示过弹窗了，全屏回到首页又只显示了引导弹窗
                TipUtil.saveShowedTip(TipUtil.TYPE_MODE_MOVE);
            });
        }
    }

    private void burySelectAudioModeDialogMode(int audioMode) {
        switch (audioMode) {
            case RecordModeUtil.FRAGMENTS_TYPE_NORMAL:
                BuryingPoint.addRecordSettingIconClick(RecorderUserAction.VALUE_NAME_RECORD_MODE,
                        RecorderUserAction.VALUE_OPTION_RECORD_TYPE_STANDARD);
                break;
            case RecordModeUtil.FRAGMENTS_TYPE_MEETING:
                BuryingPoint.addRecordSettingIconClick(RecorderUserAction.VALUE_NAME_RECORD_MODE,
                        RecorderUserAction.VALUE_OPTION_RECORD_TYPE_MEETING);
                break;
            case RecordModeUtil.FRAGMENTS_TYPE_INTERVIEW:
                BuryingPoint.addRecordSettingIconClick(RecorderUserAction.VALUE_NAME_RECORD_MODE,
                        RecorderUserAction.VALUE_OPTION_RECORD_TYPE_INTERVIEW);
                break;
            default:
                DebugUtil.e(TAG, "burySelectAudioModeDialogMode item exception");
                break;
        }
    }

    private void burySelectAudioFormatType(int audioFormat) {
        switch (audioFormat) {
            case RecorderConstant.RECORDER_AUDIO_FORMAT_WAV:
                BuryingPoint.selectAudioFormatClicked(BuryingPoint.VALUE_RECORD_AUDIO_FORMAT_WAV);
                BuryingPoint.addRecordSettingIconClick(RecorderUserAction.VALUE_NAME_RECORD_TYPE, RecorderUserAction.VALUE_OPTION_RECORD_FORMAT_WAV);
                break;
            case RecorderConstant.RECORDER_AUDIO_FORMAT_AAC_ADTS:
                BuryingPoint.selectAudioFormatClicked(BuryingPoint.VALUE_RECORD_AUDIO_FORMAT_AAC);
                BuryingPoint.addRecordSettingIconClick(RecorderUserAction.VALUE_NAME_RECORD_TYPE, RecorderUserAction.VALUE_OPTION_RECORD_FORMAT_AAC);
                break;
            case RecorderConstant.RECORDER_AUDIO_FORMAT_MP3:
                BuryingPoint.selectAudioFormatClicked(BuryingPoint.VALUE_RECORD_AUDIO_FORMAT_MP3);
                BuryingPoint.addRecordSettingIconClick(RecorderUserAction.VALUE_NAME_RECORD_TYPE, RecorderUserAction.VALUE_OPTION_RECORD_FORMAT_MP3);
                // default format，not need
                break;
        }
    }

    private void initPictureRecommendation() {
        mSettingRecordPictureRecommendation = findPreference(SETTING_RECORD_PICTURE_RECOMMENDATION);
        if (FunctionOption.IS_SUPPORT_PHOTO_RECOMMEND) {
            if (mSettingRecordPictureRecommendation != null) {
                mSettingRecordPictureRecommendation.setOnPreferenceClickListener(this);
                mSettingRecordPictureRecommendation.setOnPreferenceChangeListener(this);
                mSettingRecordPictureRecommendation.setVisible(true);
            }
            requestPermission = registerForActivityResult(new PermissionProxyActivity.Companion.RequestPermissionProxy(), this::callbackPermission);
            setPictureRecommendation(FunctionOption.isSupportPhotoMarkRecommend(), true);
        } else {
            if (mSettingRecordPictureRecommendation != null) {
                mSettingRecordPictureRecommendation.setVisible(false);
            }
        }
    }

    private void switchPictureRecommendation() {
        if (ClickUtils.isQuickClick()) return;
        BuryingPoint.addRecordSettingIconClick(RecorderUserAction.VALUE_NAME_PICTURE_TAG, RecorderUserAction.VALUE_OPTION_RECORD_TYPE_DEFAULT);
        mExecutor.execute(() -> {
            boolean isOpen = FunctionOption.isSupportPhotoMarkRecommend();
            if (isOpen) {
                setPictureRecommendation(false, false);
            } else {
                if (PermissionUtils.hasReadImagesPermission()) {
                    setPictureRecommendation(true, false);
                } else {
                    setPictureRecommendation(false, false);
                    if (requestPermission != null) {
                        requestPermission.launch(new String[]{PermissionUtils.READ_IMAGE_PERMISSION()});
                    }
                }
            }
        });
    }

    private void callbackPermission(int resultCode) {
        DebugUtil.d(TAG, "resultCode == " + resultCode);
        checkReadImagePermission(resultCode);
        setPictureRecommendation(RESULT_OK == resultCode, false);
    }

    /**
     * fromOnResume 是为了界面处于onResume查询状态的时候不要设置值
     * 这样能保证用户在没有在应用中操作过权限时
     * 去设置授予权限回来，智能图片标记推荐开关会自动打开
     *
     * @param isOpen
     * @param fromOnResume
     */
    private void setPictureRecommendation(boolean isOpen, boolean fromOnResume) {
        //U兼容
        if (BaseUtil.isAndroidUOrLater()) {
            if (!fromOnResume) {
                FunctionOption.putSupportPhotoMarkRecommend(isOpen);
            }
        } else {
            FunctionOption.putSupportPhotoMarkRecommend(isOpen);
        }
        Activity activity = getActivity();
        if (activity != null) {
            activity.runOnUiThread(() -> {
                if (mSettingRecordPictureRecommendation != null) {
                    mSettingRecordPictureRecommendation.setChecked(isOpen);
                }
            });
        }
    }

    /**
     * 当用户点击“选择图片” 且成功授予Manifest.permission.READ_MEDIA_VISUAL_USER_SELECTED
     * 如果是本次操作 返回resultCode = RESULT_FIRST_USER，需要提示用户
     * 后面点击取消则重置录制页的提示逻辑,不需要Toast提示
     *
     * @param resultCode
     */
    private void checkReadImagePermission(int resultCode) {
        if (!BaseUtil.isAndroidUOrLater()) {
            return;
        }
        if (!PermissionUtils.hasReadImagesPermission()) {
            ToastManager.showShortToast(getActivity(), BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.images_permission_record_turned_on_toast));
            mRecordInterface.setShowReadImagePermissionTips(true);
        } else {
            ToastManager.showShortToast(getActivity(), BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.images_permission_record_open_toast));
        }
    }

    /**
     * 更新三方通话录音应用开关是否打开提示
     */
    private void queryThirdAppRecordMode() {
        if ((mExecutor != null) && (mBottomRecommendPreference != null)) {
            mExecutor.execute(() -> {
                ThirdRecordResultBean resultBean = ThirdAppRecordCheckManager.queryThirdAppRecordMode();
                Message msg = sHandler.obtainMessage(SettingFragment.QUERY_THIRD_RECORD_MODE);
                msg.obj = resultBean;
                sHandler.sendMessage(msg);
            });
        }
    }

    /**
     * 录音智能命名是否打开
     */
    private void querySmartNameOpenMode() {
        if ((mExecutor != null) && (mSmartGenerationPreference != null)) {
            mExecutor.execute(() -> {
                mSupportSmartName = mSmartNameAction.checkSupportSmartName(getActivity(),
                        mConvertSupportAction.isSupportConvert(!isFromOtherApp()), true);
                DebugUtil.d(TAG, "querySmartNameOpenMode, supportSmartName:" + mSupportSmartName);
                Message msg = sHandler.obtainMessage(SettingFragment.QUERY_SMART_NAME_MODE);
                msg.obj = mSupportSmartName;
                sHandler.sendMessage(msg);
            });
        }
    }

    /**
     * 查询是否支持实时转写
     */
    private void querySpeechLanguageMode() {
        DebugUtil.d(TAG, "querySpeechLanguageMode");
        Context context = getContext();
        if (mExecutor != null && context != null) {
            mExecutor.execute(() -> {
                boolean asrSupportAndDownload = mAsrPluginManager.isAsrPluginSupportAndDownload(context);
                DebugUtil.d(TAG, "querySpeechLanguageMode asrSupportAndDownload=" + asrSupportAndDownload);
                Message msg = sHandler.obtainMessage(SettingFragment.QUERY_SPEECH_LANGUAGE_MODE);
                msg.obj = asrSupportAndDownload;
                sHandler.sendMessage(msg);
            });
        }
    }

    /**
     * 查询支持的语种列表
     */
    private void querySupportLanguage(String from) {
        DebugUtil.d(TAG, "querySupportLanguage from:" + from);
        Context context = getContext();
        if (mExecutor != null && context != null) {
            mExecutor.execute(() -> {
                boolean asrSupportAndDownload = mAsrPluginManager.isAsrPluginSupportAndDownload(context);
                DebugUtil.d(TAG, "querySupportLanguage asrSupportAndDownload=" + asrSupportAndDownload);
                if (asrSupportAndDownload) {
                    Message msg = sHandler.obtainMessage(SettingFragment.QUERY_SUPPORT_LANGUAGE);
                    sHandler.sendMessage(msg);
                }
            });
        }
    }

    @Override
    public void onClickSpan(int type) {
        DebugUtil.d(TAG, "onClickSpan" + type);
    }

    @Override
    public void onPrivacyPolicyFail(int type, @Nullable Integer pageFrom) {
        Activity activity = getActivity();
        if (activity == null || activity.isDestroyed()) {
            DebugUtil.w(TAG, "onPrivacyPolicyFail Invalid activity");
            return;
        }

        if (TYPE_PERMISSION_SMART_SHORTHAND == type) {
            mSmartNameAction.setSmartNameSwitchStatus(activity, false, true);
            getActivity().runOnUiThread(() -> {
                mSmartGenerationPreference.setChecked(false);
            });
        }
    }

    @Override
    public void onPrivacyPolicySuccess(int type, @Nullable Integer pageFrom) {
        Activity activity = getActivity();
        if (activity == null || activity.isDestroyed()) {
            DebugUtil.w(TAG, "onPrivacyPolicySuccess Invalid activity");
            return;
        }

        if (TYPE_PERMISSION_SMART_SHORTHAND == type) {
            mSmartNameAction.setSmartNameSwitchStatus(activity, true, true);
            activity.runOnUiThread(() -> {
                mSmartGenerationPreference.setChecked(true);
                continueSmartGenerationSetup();
            });
        }
    }

    private static class SettingPluginDownloadCallback implements IPluginDownloadCallback {
        private final WeakReference<SettingFragment> mWeakFragment;

        SettingPluginDownloadCallback(SettingFragment recorderFragment) {
            this.mWeakFragment = new WeakReference<>(recorderFragment);
        }

        @Override
        public void onDownLoadResult(boolean result) {
            DebugUtil.d(TAG, "onDownLoadResult, result:" + result + ", Thread:" + Thread.currentThread());
            SettingFragment recorderFragment = mWeakFragment.get();
            if (recorderFragment == null) {
                return;
            }

            Activity activity = recorderFragment.getActivity();
            if (activity == null || activity.isDestroyed()) {
                return;
            }

            if (recorderFragment.mUnifiedSummaryManager == null) {
                return;
            }
            activity.runOnUiThread(() -> recorderFragment.mSmartGenerationPreference.setChecked(result));
            if (result) {
                /*插件下载完成，查询转写是否支持，并查询转写支持语言*/
                recorderFragment.querySpeechLanguageMode();
                recorderFragment.querySupportLanguage("onDownLoadResult");
            }
        }
    }
}
