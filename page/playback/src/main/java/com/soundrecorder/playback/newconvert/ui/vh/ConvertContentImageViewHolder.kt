/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ConvertContentImageViewHolder.kt
 ** Description : ConvertContentImageViewHolder.kt
 ** Version     : 1.0
 ** Date        : 2025/07/04
 ** Author      : W9085798
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9085798       2025/07/04      1.0      create
 ***********************************************************************/

package com.soundrecorder.playback.newconvert.ui.vh

import android.view.LayoutInflater
import android.view.ViewGroup
import com.google.android.material.imageview.ShapeableImageView
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.imageload.ImageLoadData
import com.soundrecorder.imageload.ImageLoaderUtils
import com.soundrecorder.playback.R
import com.soundrecorder.playback.newconvert.ui.TextImageItemAdapter
import com.soundrecorder.playback.newconvert.view.ShapableImageViewSetupHelper
import java.lang.ref.WeakReference

class ConvertContentImageViewHolder(parent: ViewGroup, adapter: TextImageItemAdapter) : AbsContentViewHolder(
    LayoutInflater.from(parent.context)
        .inflate(R.layout.item_convert_content_image, parent, false)
) {

    companion object {
        private const val TAG = "ConvertContentImageViewHolder"
    }

    private val mImageView: ShapeableImageView = itemView.findViewById(R.id.thumbImage)

    private val setupHelper: ShapableImageViewSetupHelper = ShapableImageViewSetupHelper()

    private var mSubItemData: ConvertContentItem.ImageMetaData? = null
    private var mParentData: ConvertContentItem? = null

    private var mImageLoadData: ImageLoadData? = null

    private val mWeakAdapter = WeakReference(adapter)


    fun setData(data: ConvertContentItem.ItemMetaData, parentData: ConvertContentItem) {
        if (data is ConvertContentItem.ImageMetaData) {
            this.mSubItemData = data
            this.mParentData = parentData
            generateLayoutParameterForImage(data, parentData)
        }
    }

    private fun generateLayoutParameterForImage(
        subItemData: ConvertContentItem.ImageMetaData,
        parentData: ConvertContentItem
    ) {
        val imageClickCallback =  mWeakAdapter.get()?.mImageClickCallback
        val drawAttr = mWeakAdapter.get()?.drawAttr
        if (imageClickCallback == null || drawAttr == null) {
            DebugUtil.d(TAG, "generateLayoutParameterForImage imageClickCallback or drawAttr null")
            return
        }
        val subItemIndex = parentData.mTextOrImageItems?.indexOf(subItemData) ?: -1
        val subItemListSize = parentData.mTextOrImageItems?.size ?: -1
        val isFirstOne =
            (subItemListSize != -1) && (subItemIndex == 1) //第0个item为固定的TIME_DIVIDER类型的item，所以这个判定为index=1
        val isLastOne = (subItemListSize != -1) && (subItemIndex == (subItemListSize - 1))
        val lastItem = parentData.mTextOrImageItems?.get(subItemIndex - 1)
        val lastItemType =
            when (lastItem) {
                is ConvertContentItem.ImageMetaData -> ConvertContentItem.ItemMetaData.TYPE_IMAGE
                is ConvertContentItem.TextItemMetaData -> ConvertContentItem.ItemMetaData.TYPE_TEXT
                else -> ConvertContentItem.ItemMetaData.TYPE_TIME_DIVIDER
            }
        val viewPair = setupHelper.setUpImageView(
            mImageView.context,
            subItemData,
            parentData,
            mImageView,
            isFirstOne,
            isLastOne,
            lastItemType,
            imageClickCallback,
            drawAttr
        )
        mImageLoadData = viewPair?.second
    }

    /**
     * show speaker view animator
     */
    override fun showAnimate() {}

    /**
     * dismiss speaker view animator
     */
    override fun dismissAnimate() {}

    override fun refreshSpeaker() {}

    override fun release() {
        itemView.animate().cancel()
    }

    override fun releaseBitmapCache() {
        DebugUtil.i(TAG, "releaseBitmapCache for viewHolder")
        mImageLoadData?.let { ImageLoaderUtils.clearMemoryCacheByKey(it) }
    }
}