/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  RecorderLogInterface.kt
 * * Description : RecorderLogInterface
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.modulerouter.xlog

import android.content.Context

interface RecorderLogInterface {
    fun initLog(context: Context)
    fun flushLog(isSync: Boolean)
    fun processPushLog(context: Context?, cloudLogConfigMsg: RecordLogXConfig)
    fun processManualReportLog()
    fun processDBPrint(context: Context?)
    fun v(tag: String?, message: String?)
    fun d(tag: String?, message: String?)
    fun i(tag: String?, message: String?)
    fun w(tag: String?, message: String?)
    fun e(tag: String?, message: String?)
    fun e(tag: String?, message: String?, e: Throwable?)
}