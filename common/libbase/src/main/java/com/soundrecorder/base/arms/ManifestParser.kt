/*********************************************************************
 * * Copyright (C), 2030, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ManifestParser
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/07/19
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.base.arms

import android.content.Context
import android.content.pm.PackageManager

/**
 * 用于解析 AndroidManifest 中的 Meta 属性
 * 配合 [ConfigModule] 使用
 */
class ManifestParser(private val context: Context) {

    private fun parseModule(className: String): ConfigModule? {
        return runCatching {
            val clazz: Class<*> = Class.forName(className)
            val module = clazz.getDeclaredConstructor().newInstance()
            if (module !is ConfigModule) {
                throw IllegalStateException("Expected instanceof ConfigModule, but found: $module")
            }
            module
        }.onFailure {
            when (it.cause) {
                is ClassNotFoundException -> throw ClassNotFoundException("Unable to find ConfigModule implementation", it.cause)
                is InstantiationException -> throw InstantiationException("Unable to instantiate ConfigModule implementation for $className")
                is IllegalAccessException -> throw IllegalAccessException("Unable to instantiate ConfigModule implementation for $className")
            }
        }.getOrNull()
    }

    fun parse(): MutableList<ConfigModule> {
        val modules: MutableList<ConfigModule> = ArrayList()
        runCatching {
            val appInfo = context.packageManager.getApplicationInfo(
                context.packageName, PackageManager.GET_META_DATA
            )
            appInfo.metaData?.keySet()?.forEach {
                if (MODULE_VALUE == appInfo.metaData.get(it)) {
                    val module = parseModule(it)
                    if (module != null) {
                        modules.add(module)
                    }
                }
            }
        }.onFailure {
            if (it.cause is PackageManager.NameNotFoundException) {
                throw java.lang.RuntimeException(
                    "Unable to find metadata to parse ConfigModule",
                    it.cause
                )
            }
        }
        return modules
    }

    companion object {
        private const val MODULE_VALUE = "ConfigModule"
    }
}
