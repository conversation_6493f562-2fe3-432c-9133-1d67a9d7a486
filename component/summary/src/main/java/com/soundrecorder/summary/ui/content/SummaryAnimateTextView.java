/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SummaryAnimateTextView
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/05
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.ui.content;

import android.animation.Animator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.TypedArray;
import android.os.SystemClock;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.Spanned;
import android.util.AttributeSet;
import android.util.Log;

import androidx.annotation.Keep;
import androidx.annotation.Nullable;

import com.coui.appcompat.contextutil.COUIContextUtil;
import com.coui.appcompat.textview.COUITextView;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.common.R;
import com.soundrecorder.common.widget.animspan.COUIAnimateSpan;
import com.soundrecorder.common.widget.animspan.COUIAnimateSpan2;
import com.soundrecorder.common.widget.animspan.COUIAnimateSpanParam;

import java.util.HashMap;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;

import io.noties.markwon.core.AnimationSpan;

@Keep
public class SummaryAnimateTextView extends COUITextView {

    public static final int ANIMATE_TYPE_1 = 1;
    public static final int ANIMATE_TYPE_2 = 2;

    public static final int TYPE_SEQUENCE = 1;
    public static final int TYPE_GRADIENT = 2;
    public static final int TYPE_OFFSET = 4;
    private static final String TAG = "COUIAnimateTextView";

    private static final float DURATION_DEFAULT = 300;
    private static final float DELAY_DEFAULT = 8;
    private static final int UNSET = -1;
    private static final float OFFSET = 10f;
    private static final float VISIBLE_FACT = 0.1f;

    private final ValueAnimator mTriggerAnimator = ValueAnimator.ofFloat(0f, 1f);
    private final HashMap<Integer, COUIAnimateSpan> mSpanMap = new HashMap<>();
    private final HashMap<Integer, int[]> mAnimationState = new HashMap<>();

    private TextAnimationListener mListener;
    private CharSequence mAnimateCharSequence;
    private volatile int mStartColor;
    private volatile int mEndColor;
    private volatile int mStableColor;
    private volatile long mDuration;
    private volatile long mDelay;
    private volatile float mTranslationOffset;
    private volatile int mType;
    private volatile int mAnimateStyle = ANIMATE_TYPE_1;

    private volatile long mStartTime = UNSET;
    private volatile long mPauseDeltaTime = UNSET;
    private volatile int mCurReadyPos = UNSET;
    private volatile int mStartPos = 0;
    private volatile boolean mContinue = false;
    private volatile boolean mLastReady = false;

    public SummaryAnimateTextView(Context context) {
        this(context, null);
    }

    public SummaryAnimateTextView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, android.R.attr.textViewStyle);
    }

    public SummaryAnimateTextView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.COUIAnimateTextView, defStyleAttr, 0);
        mAnimateStyle = a.getInteger(R.styleable.COUIAnimateTextView_couiAnimateStyle, ANIMATE_TYPE_1);
        mType = a.getInteger(R.styleable.COUIAnimateTextView_couiAnimateTextType, TYPE_SEQUENCE | TYPE_GRADIENT | TYPE_OFFSET);
        mStartColor = a.getColor(R.styleable.COUIAnimateTextView_couiAnimateTextStartColor,
                COUIContextUtil.getColor(context, R.color.coui_animate_text_start_color_default));
        mEndColor = a.getColor(R.styleable.COUIAnimateTextView_couiAnimateTextEndColor,
                COUIContextUtil.getColor(context, R.color.coui_animate_text_end_color_default));
        mStableColor = a.getColor(R.styleable.COUIAnimateTextView_couiAnimateTextStableColor, getTextColors().getDefaultColor());
        mDuration = (long) a.getFloat(R.styleable.COUIAnimateTextView_couiAnimateTextDuration, DURATION_DEFAULT);
        mDelay = (long) a.getFloat(R.styleable.COUIAnimateTextView_couiAnimateTextDelay, DELAY_DEFAULT);
        mTranslationOffset = a.getFloat(R.styleable.COUIAnimateTextView_couiAnimateTextOffset, OFFSET);
        a.recycle();

        mTriggerAnimator.addUpdateListener(animation -> {
            SummaryAnimateTextView.this.postInvalidate();
            if (mListener != null) {
                mListener.onAnimationUpdate(mCurReadyPos);
            }
        });
        mTriggerAnimator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
                //do nothing
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                if (mListener != null) {
                    mListener.onAnimationEnd();
                }
            }

            @Override
            public void onAnimationCancel(Animator animation) {
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
                //do nothing
            }
        });
    }

    /**
     * set text and start animation
     *
     * @param text the text who will play animation
     * @param isReset: 是否每次都从0开始播放
     */
    public void setAnimateText(CharSequence text, boolean isReset) {
        if (text == null || text.length() == 0) {
            return;
        }
        mContinue = false;
        mLastReady = false;
        int commonStringEnd = 0;
        if (isReset) {
            //智能标题每次都从0开始播放
            mStartPos = 0;
        } else {
            if (mAnimateCharSequence != null && mAnimateCharSequence.length() > 0 && text.length() >= mAnimateCharSequence.length()) {
                //新文本是基于老文本的扩充
                String subSequence = text.toString().substring(0, mAnimateCharSequence.length());
                DebugUtil.d(TAG, "setAnimateText subSequence " + subSequence + "\nmAnimateCharSequence = " + mAnimateCharSequence.toString());
                mContinue = subSequence.equals(mAnimateCharSequence.toString());
                //由于格式转化，导致吞字，所以找到最大共有字符串，并以这个字符重做动画，尽量减轻问题
                if (!mContinue) {
                    int[] common = findCommonSubstringFromStart(text.toString(), mAnimateCharSequence.toString());
                    if (common != null && common[1] > 0) {
                        mContinue = true;
                        commonStringEnd = common[1];
                    }
                }
            }

            if (mContinue) {
                if (mAnimateCharSequence != null && mCurReadyPos == mAnimateCharSequence.length() - 1) {
                    //老文本动画已经全部结束, 从新文本增加的部分开始做动画
                    mLastReady = true;
                    mStartPos = mCurReadyPos + 1;
                    if (commonStringEnd > 0) {
                        mStartPos = commonStringEnd + 1;
                        //找到最大共同字符串后，由于此前动画已经做了，从吞字后的位置已经在mSpanMap中导致不会播放动画，所以需要先清楚掉此前的
                        if (mStartPos < mCurReadyPos + 1) {
                            resetSpanMap(mStartPos, mCurReadyPos + 1);
                        }
                    }
                }
            } else {
                //无法接续时，从头开始做动画
                mStartPos = 0;
            }
        }
        mAnimateCharSequence = text;
        Log.d(TAG, "setAnimateText: mContinue:" + mContinue
                + ",mLastReady:" + mLastReady
                + ",mCurReadyPos:" + mCurReadyPos
                + ",mReadyPos:" + mStartPos
                + ",mAnimateCharSequence length:" + mAnimateCharSequence.length()
        );
        showTextWithAnimation(mStartPos, mAnimateCharSequence.length() - 1, !mContinue);
    }

    private void resetSpanMap(int start, int end) {
        for (int index = start; index < end; index++) {
            mSpanMap.remove(index);
        }
    }

    public void setAnimateText(CharSequence text) {
        mAnimateCharSequence = text;
        mCurReadyPos = mAnimateCharSequence.length() - 1;
        setText(text);
    }

    private static int[] findCommonSubstringFromStart(String str1, String str2) {
        // 如果任一字符串为空，或者第0位就不同，直接返回 null
        if (str1.isEmpty() || str2.isEmpty() || str1.charAt(0) != str2.charAt(0)) {
            return null;
        }
        int maxLength = 0;
        int endIndex = 0;
        // 查找最长公共前缀
        int minLength = Math.min(str1.length(), str2.length());
        for (int i = 0; i < minLength; i++) {
            if (str1.charAt(i) == str2.charAt(i)) {
                maxLength++;
                endIndex = i;
            } else {
                break;
            }
        }
        // 如果找到了公共前缀，返回结果
        return maxLength > 0 ? new int[] {0, endIndex} : null;
    }


    /**
     * get text
     *
     * @return the text set by {@link #setAnimateText}
     */
    public CharSequence getAnimateCharSequence() {
        return mAnimateCharSequence;
    }

    /**
     * start animation between startPos and endPos
     *
     * @param startPos  the start position in {@link #mAnimateCharSequence} for play animation
     * @param endPos    the end position in {@link #mAnimateCharSequence} for play animation
     */
    public void showTextWithAnimation(int startPos, int endPos) {
        showTextWithAnimation(startPos, endPos, true);
    }

    private void showTextWithAnimation(int startPos, int endPos, boolean forceAnim) {
        if (mAnimateCharSequence == null || mAnimateCharSequence.length() == 0) {
            return;
        }

        if (startPos > endPos || startPos > mAnimateCharSequence.length() - 1 || endPos > mAnimateCharSequence.length() - 1) {
            return;
        }

        if (forceAnim) {
            //主动触发动画，刷新所有标记
            reset();
        }

        if (forceAnim || mStartTime == UNSET || !mContinue || mLastReady) {
            /*下面几种情况需要刷新开始时间, 否则接续即可
            (1)主动触发动画
            (2)第一次开始动画
            (3)文本无接续
            (4)文本有接续,但上个文本完成动画*/
            mStartTime = SystemClock.uptimeMillis();
        } else if (mPauseDeltaTime != UNSET) {
            //如果不需要刷新时间, 且之前有暂停动画, 从暂停进度接续
            calcStartTimeWithPauseDelta();
        }

        startAnimation(startPos, endPos, forceAnim);
    }

    void reset() {
        mContinue = false;
        mLastReady = false;
        mStartPos = 0;
        mCurReadyPos = UNSET;
        mPauseDeltaTime = UNSET;
        mSpanMap.clear();
        mAnimationState.clear();
    }

    private void startAnimation(int startPos, int endPos, boolean forceAnim) {
        SpannableString spanString = new SpannableString(mAnimateCharSequence);
        for (int i = startPos; i <= endPos; i ++) {
            final int index = i;
            final long totalDelay = mDelay * (index - startPos);
            if (!mSpanMap.containsKey(index)) {
                COUIAnimateSpanParam param = new COUIAnimateSpanParam();
                param.duration = mDuration;
                param.delay = totalDelay;
                param.textSize = getAnimationSpanTextSize(spanString, index);
                param.offset = mTranslationOffset;
                param.startColor = mStartColor;
                param.endColor = mEndColor;
                param.stableColor = getStableColor(spanString, index);
                param.runEndRunnable = () -> {
                    mCurReadyPos = index;
                    if (mCurReadyPos == mAnimateCharSequence.length() - 1) {
                        mTriggerAnimator.cancel();
                    }
                };

                AtomicBoolean alreadySet = new AtomicBoolean(false);
                param.updateRunnable = fraction -> {
                    if (fraction > VISIBLE_FACT && !alreadySet.get()) {
                        updateAnimationSpanVisible(index);
                        alreadySet.set(true);
                    }
                };
                if (mAnimateStyle == ANIMATE_TYPE_2) {
                    mSpanMap.put(index, new COUIAnimateSpan2(param));
                } else {
                    mSpanMap.put(index, new COUIAnimateSpan(param));
                }
            } else {
                Objects.requireNonNull(mSpanMap.get(index)).resetAnimateParams(mDuration, totalDelay, mTranslationOffset);
            }
            spanString.setSpan(mSpanMap.get(index), index, index + 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            Objects.requireNonNull(mSpanMap.get(index)).playAnimation(mType, mStartTime);
        }
        updateAnimationSpanInVisible(spanString);
        setText(spanString);
        mTriggerAnimator.setDuration((endPos - startPos) * mDelay + mDuration);
        if (forceAnim || !mTriggerAnimator.isRunning()) {
            mTriggerAnimator.start();
        } else if (mTriggerAnimator.isRunning() && mTriggerAnimator.isPaused()) {
            mTriggerAnimator.resume();
        }
    }

    private int getStableColor(SpannableString spanString, int index) {
        AnimationSpan[] spans = spanString.getSpans(index, index, AnimationSpan.class);
        if (spans.length == 1) {
            AnimationSpan span = spans[0];
            if (span != null) {
                int color =  spans[0].getColor();
                if (color != 0) {
                    return color;
                } else {
                    return mStableColor;
                }
            }
            return mStableColor;
        }
        return mStableColor;
    }

    private void updateAnimationSpanInVisible(SpannableString spanString) {
        AnimationSpan[] spans = spanString.getSpans(0, spanString.length(), AnimationSpan.class);
        for (AnimationSpan span : spans) {
            int spanStart = spanString.getSpanStart(span);
            int spanEnd = spanString.getSpanEnd(span);
            int[] cache = mAnimationState.get(spanStart);
            if (cache == null || cache[1] == 0) {
                int[] newCache = new int[]{spanEnd, 0};
                mAnimationState.put(spanStart, newCache);
                span.invisible();
            }
        }
    }

    private void updateAnimationSpanVisible(int index) {
        Spanned spanString = null;
        if (getText() instanceof Spanned) {
            spanString = (Spanned) getText();
        }
        if (spanString == null) {
            return;
        }
        int[] cache = mAnimationState.get(index);
        if (cache != null) {
            int spanEnd = cache[0];
            int end = Math.min(spanEnd, spanString.length());
            AnimationSpan[] spans = spanString.getSpans(index, end, AnimationSpan.class);
            for (AnimationSpan span : spans) {
                int start = spanString.getSpanStart(span);
                if (index == start) {
                    mAnimationState.put(index, new int[]{spanEnd, 1});
                    span.visible();
                    break;
                }
            }
        }
    }

    private void setAnimationSpanVisible(SpannableString spanString) {
        AnimationSpan[] spans = spanString.getSpans(0, spanString.length(), AnimationSpan.class);
        for (AnimationSpan span : spans) {
            span.visible();
        }
    }

    private float getAnimationSpanTextSize(SpannableString spanString, int index) {
        AnimationSpan[] spans = spanString.getSpans(index, index, AnimationSpan.class);
        for (AnimationSpan span : spans) {
            return span.getTextSize(getTextSize());
        }
        return getTextSize();
    }


    /**
     * cancel animation and set text with stable status
     */
    public void cancelAnimation() {
        DebugUtil.d(TAG, "cancelAnimation");
        if (mAnimateCharSequence == null || mAnimateCharSequence.length() == 0) {
            return;
        }
        SpannableString spanString = new SpannableString(mAnimateCharSequence);
        for (int i = 0; i < mAnimateCharSequence.length() ; i ++) {
            if (mSpanMap.containsKey(i)) {
                Objects.requireNonNull(mSpanMap.get(i)).cancelAnimation();
            }
            setAnimationSpanVisible(spanString);
        }
        mTriggerAnimator.cancel();
        postInvalidate();
        reset();
    }

    /**
     * pause animation and keep text status during animation
     */
    public void pauseAnimation() {
        if (mAnimateCharSequence == null || mAnimateCharSequence.length() == 0 || mPauseDeltaTime != UNSET) {
            return;
        }

        if (mTriggerAnimator.isRunning() && !mTriggerAnimator.isPaused()) {
            mPauseDeltaTime = SystemClock.uptimeMillis() - mStartTime;
            mTriggerAnimator.pause();
        }
        for (int i = 0; i < mAnimateCharSequence.length() ; i ++) {
            if (mSpanMap.containsKey(i)) {
                Objects.requireNonNull(mSpanMap.get(i)).pauseAnimation();
            }
        }
        postInvalidate();
    }

    public boolean isAnimating() {
        return mTriggerAnimator.isRunning() && !mTriggerAnimator.isPaused();
    }

    /**
     * resume animation if it has paused
     */
    public void resumeAnimation() {
        if (mAnimateCharSequence == null || mAnimateCharSequence.length() == 0 || mPauseDeltaTime == UNSET) {
            return;
        }

        if (mTriggerAnimator.isRunning() && mTriggerAnimator.isPaused()) {
            calcStartTimeWithPauseDelta();
            mTriggerAnimator.resume();
        }
        for (int i = 0; i < mAnimateCharSequence.length() ; i ++) {
            if (mSpanMap.containsKey(i)) {
                Objects.requireNonNull(mSpanMap.get(i)).resumeAnimation(mStartTime);
            }
        }
        postInvalidate();
    }

    private void calcStartTimeWithPauseDelta() {
        //恢复暂停时保存的动画进度
        mStartTime = SystemClock.uptimeMillis() - mPauseDeltaTime;
        mPauseDeltaTime = UNSET;
    }

    /**
     * set type for animation
     * TYPE_SEQUENCE = 1;
     * TYPE_GRADIENT = 2;
     * TYPE_OFFSETY = 4;
     */
    public void setType(int type) {
        mType = type;
    }

    /**
     * set start color for gradient-color animation
     *
     * color will change like: startColor -> endColor -> stableColor
     */
    public void setStartColor(int color) {
        mStartColor = color;
    }

    /**
     * set end color for gradient-color animation
     *
     * color will change like: startColor -> endColor -> stableColor
     */
    public void setEndColor(int color) {
        mEndColor = color;
    }

    /**
     * set stable color for gradient-color animation
     *
     * color will change like: startColor -> endColor -> stableColor
     */
    public void setStableColor(int color) {
        mStableColor = color;
    }

    /**
     * set offset for translationY animation
     */
    public void setTranslationOffset(float offset) {
        mTranslationOffset = offset;
    }

    /**
     * set duration for single character
     */
    public void setDuration(long duration) {
        mDuration = duration;
    }

    /**
     * set delay between two characters' animation
     */
    public void setDelay(long delay) {
        mDelay = delay;
    }

    public void setAnimationListener(TextAnimationListener listener) {
        mListener = listener;
    }

    @Keep
    public interface TextAnimationListener {
        void onAnimationEnd();
        default void onAnimationUpdate(int curReadyPos) {
        }
    }
}