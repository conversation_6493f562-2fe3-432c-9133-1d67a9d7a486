/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CollectionInfoContentFragment.kt
 * * Description : 个人信息收集明示清单-详情页
 * * Version     : 1.0
 * * Date        : 2024/8/20
 * * Author      : ********
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.privacypolicy

import android.app.Activity
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.ViewModelProvider
import com.coui.appcompat.preference.COUIPreference
import com.coui.appcompat.toolbar.COUIToolbar
import com.soundrecorder.base.BaseActivity
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.view.RootViewPersistentInsetsCallback
import com.soundrecorder.common.databean.CollectionInfo
import com.soundrecorder.common.utils.taskbar.TaskBarUtil
import com.soundrecorder.common.widget.AutoIndentPreferenceFragment
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant

class CollectionInfoContentFragment : AutoIndentPreferenceFragment() {
    companion object {
        private const val TAG = "CollectionInfoContentFragment"
        private const val KEY_FRAGMENT_TYPE_VALUE = "key_fragment_type_value"
        private const val KEY_FRAGMENT_TITLE_VALUE = "key_fragment_title_value"
        private const val KEY_FRAGMENT_COLLECTION_TYPE_VALUE = "key_fragment_collection_type_value"

        private const val NOTE_SETTING_INFORMATION_HEADER = "pref_information_header"

        //private const val KEY_PREF_COLLECTION_CONTENT_TIME = "key_pref_collection_content_time"
        private const val KEY_PREF_COLLECTION_CONTENT_PURPOSE = "key_pref_collection_content_purpose"
        private const val KEY_PREF_COLLECTION_CONTENT_USAGE_SCENARIOS = "key_pref_collection_content_usage_scenarios"
        private const val KEY_PREF_COLLECTION_CONTENT_COLLECTION_COUNT = "key_pref_collection_content_collection_count"
        private const val KEY_PREF_COLLECTION_CONTENT_CONTENT_INFO = "key_pref_collection_content_content_info"

        private const val END_SUFFIX = "******"
        private const val END_SUFFIX_LENGTH = 6

        fun newInstance(
            title: String?,
            type: Int = PrivacyPolicyConstant.TYPE_PERSONAL_INFORMATION_PROTECTION_POLICY,
            collectionType: Int
        ): CollectionInfoContentFragment {
            val args = Bundle().apply {
                putString(KEY_FRAGMENT_TITLE_VALUE, title)
                putInt(KEY_FRAGMENT_TYPE_VALUE, type)
                putInt(KEY_FRAGMENT_TYPE_VALUE, type)
                putInt(KEY_FRAGMENT_COLLECTION_TYPE_VALUE, collectionType)
            }
            val fragment = CollectionInfoContentFragment()
            fragment.arguments = args
            return fragment
        }
    }

    private var mSettingInformationHeader: CollectionInfoHeaderPreference? = null
    private var mPurposePreference: COUIPreference? = null
    private var mUsageScenariosPreference: COUIPreference? = null
    private var mCountPreference: COUIPreference? = null
    private var mContentInfoPreference: COUIPreference? = null
    private var collectionType = 0

    private val viewModel: CollectionInfoViewModel by lazy {
        ViewModelProvider(this)[CollectionInfoViewModel::class.java]
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val view = super.onCreateView(inflater, container, savedInstanceState)
        initActionBar(view)
        listView?.isForceDarkAllowed = false
        addPreferencesFromResource(R.xml.preference_collection_info_content)
        collectionType = arguments?.getInt(KEY_FRAGMENT_COLLECTION_TYPE_VALUE) ?: 0
        initPreference()
        initViewObserver()
        viewModel.loadCollectionData(getCollectionTypeForDb())
        return view
    }

    private fun initPreference() {
        mPurposePreference = findPreference(KEY_PREF_COLLECTION_CONTENT_PURPOSE)
        mUsageScenariosPreference = findPreference(KEY_PREF_COLLECTION_CONTENT_USAGE_SCENARIOS)
        mCountPreference = findPreference(KEY_PREF_COLLECTION_CONTENT_COLLECTION_COUNT)
        mContentInfoPreference = findPreference(KEY_PREF_COLLECTION_CONTENT_CONTENT_INFO)
        mSettingInformationHeader = findPreference(NOTE_SETTING_INFORMATION_HEADER)
        mSettingInformationHeader?.setOnMenuSelectDayListener(object :
            CollectionInfoHeaderPreference.OnMenuSelectDayListener {
            override fun onMenuSelectDay(day: Int) {
                viewModel.filterCollectionList(day)
            }
        })
    }

    private fun initViewObserver() {
        viewModel.currentCollectionList.observe(viewLifecycleOwner) { list ->
            updatePreferenceData(list)
        }
    }

    private fun updatePreferenceData(curCollectionList: List<CollectionInfo>) {
        when (collectionType) {
            com.soundrecorder.common.R.string.key_pref_collection_record_audio -> updateAudioPreference(curCollectionList)
            com.soundrecorder.common.R.string.key_pref_collection_device_duid -> updateDevicePreference(curCollectionList)
            com.soundrecorder.common.R.string.key_pref_collection_device_brand -> updateDevicePreference(curCollectionList)
            com.soundrecorder.common.R.string.key_pref_collection_os_version -> updateDevicePreference(curCollectionList)
            com.soundrecorder.common.R.string.key_pref_collection_device_model -> updateDevicePreference(curCollectionList)
            com.soundrecorder.common.R.string.key_pref_collection_contact_qq -> updateContactPreference(curCollectionList)
            com.soundrecorder.common.R.string.key_pref_collection_data_error_log_report -> updateDataPreference(curCollectionList)
            com.soundrecorder.common.R.string.key_pref_collection_data_buried -> updateDataPreference(curCollectionList)
            com.soundrecorder.common.R.string.key_pref_collection_data_feedback_content -> updateDataPreference(curCollectionList)
        }
    }

    private fun getCollectionTypeForDb(): String {
        when (collectionType) {
            com.soundrecorder.common.R.string.key_pref_collection_record_audio -> return CollectionInfo.COLLECTION_TYPE_RECORD_AUDIO
            com.soundrecorder.common.R.string.key_pref_collection_device_duid -> return CollectionInfo.COLLECTION_TYPE_OPEN_ID
            com.soundrecorder.common.R.string.key_pref_collection_device_brand -> return CollectionInfo.COLLECTION_TYPE_BRAND
            com.soundrecorder.common.R.string.key_pref_collection_os_version -> return CollectionInfo.COLLECTION_TYPE_OS
            com.soundrecorder.common.R.string.key_pref_collection_device_model -> return CollectionInfo.COLLECTION_TYPE_MODEL
            com.soundrecorder.common.R.string.key_pref_collection_contact_qq -> return CollectionInfo.COLLECTION_TYPE_CONTACT
            com.soundrecorder.common.R.string.key_pref_collection_data_error_log_report -> return CollectionInfo.COLLECTION_TYPE_LOG
            com.soundrecorder.common.R.string.key_pref_collection_data_buried -> return CollectionInfo.COLLECTION_TYPE_STATISTICS
            com.soundrecorder.common.R.string.key_pref_collection_data_feedback_content -> return CollectionInfo.COLLECTION_TYPE_FEEDBACK
        }
        return CollectionInfo.COLLECTION_TYPE_NONE
    }

    /**
     * 音频
     */
    private fun updateAudioPreference(curCollectionList: List<CollectionInfo>) {
        mPurposePreference?.setSummary(R.string.collection_info_convert_purpose)
        mUsageScenariosPreference?.setSummary(R.string.collection_info_usage_scenarios_convert)

        mCountPreference?.summary = String.format(
            getString(R.string.collection_info_content_collection_count_subtitle),
            curCollectionList.size
        )
        mContentInfoPreference?.setSummary(R.string.collection_info_content_info_subtitle)
    }

    /**
     * 错误日志报告/埋点信息/反馈内容附件（文字、图片）
     */
    private fun updateDataPreference(curCollectionList: List<CollectionInfo>) {
        var count = 0
        if (curCollectionList.isNotEmpty()) {
            curCollectionList.forEach {
                count += it.count
            }
        }
        mPurposePreference?.setSummary(R.string.collection_info_data_feedback_purpose)
        mUsageScenariosPreference?.setSummary(R.string.collection_info_device_scenario)
        mCountPreference?.summary = String.format(
            getString(R.string.collection_info_content_collection_count_subtitle),
            count
        )
        mContentInfoPreference?.setSummary(R.string.collection_info_content_info_subtitle)
    }

    /**
     * QQ号/手机号/邮箱
     */
    private fun updateContactPreference(curCollectionList: List<CollectionInfo>) {
        mPurposePreference?.setSummary(R.string.collection_info_contact_purpose)
        mUsageScenariosPreference?.setSummary(R.string.collection_info_device_scenario)
        mCountPreference?.summary = String.format(
            getString(R.string.collection_info_content_collection_count_subtitle),
            curCollectionList.size
        )
        if (curCollectionList.isNotEmpty()) {
            val sb = StringBuilder()
            curCollectionList.forEach { collectionInfo ->
                if (collectionInfo.isContact()) {
                    sb.append(collectionInfo.content).append(",")
                }
            }
            if (sb.lastIndexOf(",") != -1) {
                sb.deleteCharAt(sb.lastIndexOf(","))
            }
            mContentInfoPreference?.summary = sb.toString()
        } else {
            mContentInfoPreference?.setSummary(R.string.collection_info_content_info_subtitle)
        }
    }

    /**
     * duid/设备品牌/OS版本/机型
     */
    private fun updateDevicePreference(curCollectionList: List<CollectionInfo>) {
        //录音中duid 记录的是 open_id
        mPurposePreference?.setSummary(R.string.collection_info_device_purpose)
        mUsageScenariosPreference?.setSummary(R.string.collection_info_device_scenario)
        mCountPreference?.summary = String.format(
            getString(R.string.collection_info_content_collection_count_subtitle),
            curCollectionList.size
        )
        if (curCollectionList.isNotEmpty()) {
            val lastContent = curCollectionList[0].content
            var newContentStr = "/"
            if (curCollectionList[0].isOpenId()) {
                if (!TextUtils.isEmpty(lastContent) && lastContent?.length!! > END_SUFFIX_LENGTH) {
                    newContentStr = lastContent.substring(0, lastContent.length - END_SUFFIX_LENGTH)
                    newContentStr += END_SUFFIX
                } else {
                    if (lastContent != null) {
                        newContentStr = lastContent
                    }
                }
                mContentInfoPreference?.summary = newContentStr
            } else {
                mContentInfoPreference?.summary = lastContent
            }
        } else {
            mContentInfoPreference?.setSummary(R.string.collection_info_content_info_subtitle)
        }
    }

    private fun initActionBar(view: View?) {
        if (view == null) {
            return
        }
        val title = arguments?.getString(KEY_FRAGMENT_TITLE_VALUE) ?: ""
        DebugUtil.d(TAG, "initActionBar, title:$title")
        val activity = activity as? AppCompatActivity? ?: return
        val toolbar = view.findViewById<COUIToolbar>(R.id.toolbar)
        activity.setSupportActionBar(toolbar)
        val actionBar = activity.supportActionBar
        actionBar?.setHomeButtonEnabled(true)
        actionBar?.setDisplayHomeAsUpEnabled(true)
        actionBar?.title = title
        initiateWindowInsets(activity)
    }

    private fun initiateWindowInsets(activity: Activity) {
        WindowCompat.setDecorFitsSystemWindows(activity.window, false)
        activity.findViewById<View>(R.id.root_layout)?.let {
            val callback: RootViewPersistentInsetsCallback = object : RootViewPersistentInsetsCallback() {
                override fun onApplyInsets(v: View, insets: WindowInsetsCompat) {
                    super.onApplyInsets(v, insets)
                    DebugUtil.i(TAG, "onApplyInsets")
                    TaskBarUtil.setNavigationColorOnSupportTaskBar(
                        navigationHeight = insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.navigationBars()).bottom,
                        activity = activity,
                        defaultNoTaskBarColor = (activity as? BaseActivity)?.navigationBarColor()
                    )
                }
            }
            ViewCompat.setOnApplyWindowInsetsListener(it, callback)
        }
    }
}