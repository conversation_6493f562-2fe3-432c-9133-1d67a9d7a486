/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CollectionInfoViewModel.kt
 ** Description : View model for collection info
 ** Version     : 1.0
 ** Date        : 2025/05/19
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  Jiafei.Liu     2025/05/19     1.0      create
 ***********************************************************************/
package com.soundrecorder.privacypolicy

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.soundrecorder.common.databean.CollectionInfo
import com.soundrecorder.common.databean.CollectionMenuTime
import com.soundrecorder.common.db.CollectionInfoDbUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class CollectionInfoViewModel : ViewModel() {
    companion object {
        private fun getTimeMillis(day: Int): Long {
            return when (day) {
                CollectionMenuTime.COLLECTION_TIME_DAY_7 -> CollectionInfoDbUtils.TIME_MILLIS_DAY7
                CollectionMenuTime.COLLECTION_TIME_MONTH_1 -> CollectionInfoDbUtils.TIME_MILLIS_MONTH1
                CollectionMenuTime.COLLECTION_TIME_MONTH_3 -> CollectionInfoDbUtils.TIME_MILLIS_MONTH3
                CollectionMenuTime.COLLECTION_TIME_YEAR -> CollectionInfoDbUtils.TIME_MILLIS_YEAR
                else -> CollectionInfoDbUtils.TIME_MILLIS_DAY7
            }
        }
    }

    private val _allCollectionList = MutableLiveData<List<CollectionInfo>>()
    private val _currentCollectionList = MutableLiveData<List<CollectionInfo>>()

    val currentCollectionList: LiveData<List<CollectionInfo>> get() = _currentCollectionList

    fun loadCollectionData(collectionType: String) {
        viewModelScope.launch(Dispatchers.IO) {
            val list = CollectionInfoDbUtils.queryCollectionInfos(collectionType)
            withContext(Dispatchers.Main) {
                _allCollectionList.value = list
                filterCollectionList(CollectionMenuTime.COLLECTION_TIME_DAY_7)
            }
        }
    }

    fun filterCollectionList(day: Int = CollectionMenuTime.COLLECTION_TIME_DAY_7) {
        _allCollectionList.value?.let { allList ->
            val filteredList = allList.filter { collectionInfo ->
                val subTime = System.currentTimeMillis() - collectionInfo.dateCreated
                subTime >= 0 && subTime <= getTimeMillis(day)
            }
            _currentCollectionList.value = filteredList
        }
    }
}